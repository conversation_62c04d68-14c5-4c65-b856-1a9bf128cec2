import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import apiClient from '@/services/apiClient'; // import your axios instance
import centralizedSocketManager from '@/services/centralizedSocketManager'; // import centralized socket manager
import type { PaymentReceivingInfo } from '@/types/payerNegotiation'; // Adjust path if PaymentReceivingInfo is elsewhere

// Define the structure of the user object we store
interface UserInfo {
  id: string;
  email: string;
  emailVerified?: boolean; // Make optional if not always present initially
  phoneNumber?: string | null; // Add phone number
  phoneVerified?: boolean; // Add phone verification status
  username?: string; // Username (may be email or separate field)
  createdAt?: string; // Date when account was created
  reputationScore?: number; // Raw reputation score
  reputationLevel?: number; // Calculated level (1-5)
  profile?: { // Added nested profile object
    avatarUrl?: string | null;
    // defaultPaymentReceivingInfo could also be nested here if backend sends it nested
  } | null;
  defaultPaymentReceivingInfo?: PaymentReceivingInfo | null; // Added this line at the root
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem('authToken'));
  const storedUserJson = localStorage.getItem('userInfo');
  const user = ref<UserInfo | null>(storedUserJson ? JSON.parse(storedUserJson) : null);

  const isAuthenticated = computed(() => !!token.value && !!user.value);
  function login(newToken: string, newUserInfo: UserInfo) {
    token.value = newToken;
    user.value = newUserInfo; // newUserInfo from backend should now include defaultPaymentReceivingInfo
    localStorage.setItem('authToken', newToken);
    localStorage.setItem('userInfo', JSON.stringify(newUserInfo));

    // Initialize socket connection with fresh token after successful login
    // This ensures socket uses the new token, not any previously expired one
    console.log('[AuthStore] User logged in, initializing socket connection...');
    initializeSocketConnection();
  }

  // Separate method for socket initialization that can be called from different places
  function initializeSocketConnection() {
    if (!isAuthenticated.value) {
      console.warn('[AuthStore] Cannot initialize socket: User not authenticated');
      return;
    }

    console.log('[AuthStore] Initializing socket connection...');
    centralizedSocketManager.initializeSocket().catch(error => {
      console.error('[AuthStore] Failed to initialize socket:', error);

      // Check if this is an authentication error
      if (centralizedSocketManager.isInAuthErrorState()) {
        console.warn('[AuthStore] Socket authentication failed - user may need to re-login');
        // Don't automatically logout here as the user might still have a valid session
        // Let the API interceptor handle this when they make their next API call
      }
    });
  }  function logout() {
    // Clear reactive state first
    token.value = null;
    user.value = null;
    
    // Clear localStorage
    localStorage.removeItem('authToken');
    localStorage.removeItem('userInfo');
    
    // Disconnect socket and clean up listeners to prevent memory leaks
    // Wrap in try-catch to ensure logout completes even if socket disconnect fails
    try {
      centralizedSocketManager.disconnect();
    } catch (error) {
      console.error('[AuthStore] Error during socket disconnect:', error);
      // Continue with logout even if socket disconnect fails
    }
    
    // Navigation should be handled in the component
  }

  // Function to initialize state from localStorage on app load
  function initializeAuth() {
    const storedToken = localStorage.getItem('authToken');
    const storedUser = localStorage.getItem('userInfo');
    if (storedToken && storedUser) {
      token.value = storedToken;
      try {
        user.value = JSON.parse(storedUser) as UserInfo; // Cast to ensure type
      } catch (e) {
        console.error('Error parsing stored user info:', e);
        // Clear invalid stored data
        logout();
      }
    } else {
      // Ensure state is clear if nothing is stored
      token.value = null;
      user.value = null;
    }
  }

  // Action to update phone verification status in the store
  function updatePhoneVerificationStatus(status: boolean, phoneNumber?: string) {
    if (user.value) {
      user.value = {
        ...user.value,
        phoneVerified: status,
        // Optionally update phone number if provided and different
        ...(phoneNumber && { phoneNumber: phoneNumber }),
      };
      // Update localStorage as well
      localStorage.setItem('userInfo', JSON.stringify(user.value));
    }
  }  // fetchUserProfile action
  async function fetchUserProfile() {
    try {
      // apiClient.get<UserInfo> will expect the backend to return the UserInfo structure
      const response = await apiClient.get<UserInfo>('/auth/me');
      user.value = response.data; // This will now include defaultPaymentReceivingInfo if sent by backend
      localStorage.setItem('userInfo', JSON.stringify(user.value));
      return user.value;
    } catch (e) {
      console.error('fetchUserProfile error', e);
      // Potentially logout if 401, though apiClient might handle this
      throw e;
    }
  }
  return {
    token,
    user,
    isAuthenticated,
    login,
    logout,
    initializeAuth,
    initializeSocketConnection, // Add the new method
    updatePhoneVerificationStatus, // Expose the new action
    fetchUserProfile, // expose it here
  };
});
