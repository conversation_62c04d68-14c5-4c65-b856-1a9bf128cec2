import { <PERSON>o } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { ClientReportPayloadSchema, type ClientReportResponse } from '../types/schemas/debugSchemas';
import { GetReportsQuerySchema, type GetAllReportsResponse, type GetReportByIdResponse } from '../types/schemas/adminDebugSchemas';
import { ClientLogService } from '../services/clientLogService';
import { authMiddleware } from '../middleware/auth';

/**
 * Debug routes for client-side logging and reporting
 */
export default function createDebugRoutes(clientLogService: ClientLogService) {
  const router = new Hono();

  /**
   * POST /report-issue
   * Receive and store client-side debug reports
   */
  router.post(
    '/report-issue',
    zValidator('json', ClientReportPayloadSchema),
    async (c) => {
      try {
        const reportData = c.req.valid('json');
        
        // Save the report using the service
        const reportId = await clientLogService.saveReport(reportData);

        const response: ClientReportResponse = {
          success: true,
          message: 'Debug report received successfully. Thank you for helping us improve!',
          reportId,
        };

        return c.json(response, 201);

      } catch (error: any) {
        console.error('❌ [DebugRoutes] Failed to process debug report:', error);

        const response: ClientReportResponse = {
          success: false,
          message: 'Failed to process debug report. Please try again or contact support.',
        };

        return c.json(response, 500);
      }
    }
  );
  /**
   * GET /stats (for future admin use)
   * Get comprehensive statistics about debug reports and log storage
   */
  router.get('/stats', async (c) => {
    try {
      const [reportStats, logDirectoryStats] = await Promise.all([
        clientLogService.getReportStats(),
        clientLogService.getLogDirectoryStats()
      ]);

      return c.json({
        success: true,
        data: {
          reports: reportStats,
          storage: {
            ...logDirectoryStats,
            totalSizeMB: Math.round(logDirectoryStats.totalSize / 1024 / 1024 * 100) / 100,
          },
          logDirectory: clientLogService.getLogDirectory(),
        },
      });
    } catch (error: any) {
      console.error('❌ [DebugRoutes] Failed to get report stats:', error);
      return c.json({
        success: false,
        message: 'Failed to retrieve report statistics',
      }, 500);
    }
  });

  /**
   * POST /cleanup (for admin use)
   * Manually trigger log cleanup and rotation
   */
  router.post('/cleanup', async (c) => {
    try {
      await clientLogService.performMaintenance();
      
      return c.json({
        success: true,
        message: 'Log maintenance completed successfully',
      });
    } catch (error: any) {
      console.error('❌ [DebugRoutes] Failed to perform log cleanup:', error);
      return c.json({
        success: false,
        message: 'Failed to perform log maintenance',
      }, 500);
    }
  });

  // ========================================
  // ADMIN DEBUG DASHBOARD ROUTES
  // ========================================

  /**
   * GET /admin/reports
   * Get paginated list of debug reports with filtering and sorting
   */
  router.get(
    '/admin/reports',
    authMiddleware, // Protect with authentication
    zValidator('query', GetReportsQuerySchema),
    async (c) => {
      try {
        const queryParams = c.req.valid('query');
        
        const result = await clientLogService.getAllReports(queryParams);

        const response: GetAllReportsResponse = {
          success: true,
          data: result,
        };

        return c.json(response);

      } catch (error: any) {
        console.error('❌ [DebugRoutes] Failed to get reports:', error);

        const response: GetAllReportsResponse = {
          success: false,
          message: 'Failed to retrieve debug reports',
        };

        return c.json(response, 500);
      }
    }
  );

  /**
   * GET /admin/reports/:reportId
   * Get detailed information for a specific report
   */
  router.get(
    '/admin/reports/:reportId',
    authMiddleware, // Protect with authentication
    async (c) => {
      try {
        const reportId = c.req.param('reportId');
        
        if (!reportId) {
          const response: GetReportByIdResponse = {
            success: false,
            data: null,
            message: 'Report ID is required',
          };
          return c.json(response, 400);
        }

        const report = await clientLogService.getReportById(reportId);

        const response: GetReportByIdResponse = {
          success: true,
          data: report,
          message: report ? undefined : 'Report not found',
        };

        return c.json(response, report ? 200 : 404);

      } catch (error: any) {
        console.error('❌ [DebugRoutes] Failed to get report by ID:', error);

        const response: GetReportByIdResponse = {
          success: false,
          data: null,
          message: 'Failed to retrieve debug report',
        };

        return c.json(response, 500);
      }
    }
  );

  /**
   * GET /admin/reports/:reportId/export
   * Export a specific report as JSON file download
   */
  router.get(
    '/admin/reports/:reportId/export',
    authMiddleware, // Protect with authentication
    async (c) => {
      try {
        const reportId = c.req.param('reportId');
        
        if (!reportId) {
          return c.json({
            success: false,
            message: 'Report ID is required',
          }, 400);
        }

        const report = await clientLogService.getReportById(reportId);

        if (!report) {
          return c.json({
            success: false,
            message: 'Report not found',
          }, 404);
        }

        // Set headers for file download
        c.header('Content-Type', 'application/json');
        c.header('Content-Disposition', `attachment; filename="debug_report_${reportId}.json"`);

        return c.json(report);

      } catch (error: any) {
        console.error('❌ [DebugRoutes] Failed to export report:', error);

        return c.json({
          success: false,
          message: 'Failed to export debug report',
        }, 500);
      }
    }
  );

  return router;
}
