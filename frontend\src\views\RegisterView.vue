<template>
  <div class="register-page" :class="{ 'rtl': isRTL, 'ltr': !isRTL }" :dir="isRTL ? 'rtl' : 'ltr'">
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="header-content">          <router-link to="/" class="logo">
            <img 
              :src="isDark ? '/logo-dark.webp' : '/logo-light.webp'" 
              :alt="t('landing.heroTitle')"
              class="logo-image"
            />
          </router-link>
            <div class="header-actions">
            <ThemeToggle class="theme-toggle" />
            <LanguageSelector class="language-selector" />
            <router-link to="/login">
              <n-button quaternary class="signin-btn">{{ t('auth.signIn') }}</n-button>
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <!-- Background Animation -->
    <div class="register-background">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
    </div>

    <div class="register-container">
      <div class="register-card">        <div class="register-header">          <div class="brand-logo">
            <img 
              :src="isDark ? '/logo-dark.webp' : '/logo-light.webp'" 
              :alt="t('landing.heroTitle')"
              class="card-logo-image"
            />
          </div>
          <h1 class="register-title">{{ t('auth.signUp') }}</h1>
          <p class="register-subtitle">{{ t('auth.joinCommunity') }}</p>
        </div>

        <n-form ref="formRef" :model="formValue" :rules="rules" @submit.prevent="handleRegister" class="register-form">          <n-form-item path="email" :label="t('auth.email')" :show-feedback="false" class="form-item-with-feedback">
            <div class="input-wrapper">
              <n-input 
                v-model:value="formValue.email" 
                :placeholder="t('auth.enterEmail')" 
                size="large"
                class="auth-input"
                :loading="checkingEmail"
                :status="emailStatus"
                :input-props="{ autocomplete: 'email' }"
                @input="handleEmailInput"
                @blur="handleEmailBlur"
              >
                <template #prefix>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/>
                    </svg>
                  </n-icon>
                </template>
                <template #suffix v-if="formValue.email && !checkingEmail">
                  <n-icon :color="emailStatus === 'success' ? '#18a058' : emailStatus === 'error' ? '#d03050' : undefined">
                    <svg v-if="emailStatus === 'success'" viewBox="0 0 24 24">
                      <path fill="currentColor" d="M9,20.42L2.79,14.21L5.62,11.38L9,14.76L18.88,4.88L21.71,7.71L9,20.42Z"/>
                    </svg>
                    <svg v-else-if="emailStatus === 'error'" viewBox="0 0 24 24">
                      <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                  </n-icon>
                </template>
              </n-input>
              <Transition name="feedback-slide" mode="out-in">
                <div v-if="emailMessage" class="feedback-message" :class="{ 'error': emailStatus === 'error', 'success': emailStatus === 'success' }">
                  {{ emailMessage }}
                </div>
              </Transition>
            </div>
          </n-form-item>          <n-form-item path="username" :label="t('auth.username')" :show-feedback="false" class="form-item-with-feedback">
            <div class="input-wrapper">
              <n-input 
                v-model:value="formValue.username" 
                :placeholder="t('auth.enterUsername')" 
                size="large"
                class="auth-input"
                :loading="checkingUsername"
                :status="usernameStatus"
                @input="handleUsernameInput"
                @blur="handleUsernameBlur"
              >
                <template #prefix>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                    </svg>
                  </n-icon>
                </template>
                <template #suffix v-if="formValue.username && !checkingUsername">
                  <n-icon :color="usernameStatus === 'success' ? '#18a058' : usernameStatus === 'error' ? '#d03050' : undefined">
                    <svg v-if="usernameStatus === 'success'" viewBox="0 0 24 24">
                      <path fill="currentColor" d="M9,20.42L2.79,14.21L5.62,11.38L9,14.76L18.88,4.88L21.71,7.71L9,20.42Z"/>
                    </svg>
                    <svg v-else-if="usernameStatus === 'error'" viewBox="0 0 24 24">
                      <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                  </n-icon>
                </template>
              </n-input>
              <Transition name="feedback-slide" mode="out-in">
                <div v-if="usernameMessage" class="feedback-message" :class="{ 'error': usernameStatus === 'error', 'success': usernameStatus === 'success' }">
                  {{ usernameMessage }}
                </div>
              </Transition>
              <Transition name="feedback-slide" mode="out-in">
                <div v-if="usernameSuggestions.length > 0" class="username-suggestions">
                  <n-text depth="3" style="font-size: 12px;">{{ t('auth.usernameSuggestions') }}:</n-text>
                  <div class="suggestions-list">
                    <n-tag 
                      v-for="suggestion in usernameSuggestions.slice(0, 3)" 
                      :key="suggestion"
                      @click="selectSuggestion(suggestion)"
                      style="cursor: pointer; margin: 2px;"
                      size="small"
                      type="info"
                    >
                      {{ suggestion }}
                    </n-tag>
                  </div>
                </div>
              </Transition>
            </div>
          </n-form-item>          <n-form-item path="password" :label="t('auth.password')" class="form-item-with-feedback">
            <div class="input-wrapper">
              <n-input
                v-model:value="formValue.password"
                type="password"
                show-password-on="click"
                :placeholder="t('auth.enterPasswordMinChars')"
                size="large"
                class="auth-input"
                :input-props="{ autocomplete: 'new-password' }"
              >
                <template #prefix>
                  <n-icon>
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"/>
                    </svg>
                  </n-icon>
                </template>
              </n-input>
            </div>
          </n-form-item>
          
          <n-form-item path="acceptTerms" :show-label="false" class="terms-item">            <n-checkbox v-model:checked="formValue.acceptTerms" class="terms-checkbox">              <span class="terms-text">
                {{ t('auth.agreeToTerms') }} 
                <router-link to="/terms" target="_blank" class="terms-link">{{ t('auth.termsOfService') }}</router-link> 
                {{ t('auth.and') }} 
                <router-link to="/privacy" target="_blank" class="terms-link">{{ t('auth.privacyPolicy') }}</router-link>
              </span>
            </n-checkbox>
          </n-form-item>
          
          <n-form-item>
            <n-button 
              type="primary" 
              attr-type="submit" 
              :loading="loading" 
              :disabled="!formValue.acceptTerms" 
              block 
              size="large"
              class="auth-submit-btn"
            >
              <n-icon class="button-icon" v-if="!loading">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                </svg>
              </n-icon>
              {{ t('auth.createAccount') }}
            </n-button>
          </n-form-item>
            <n-alert v-if="error" :title="t('errors.registration')" type="error" closable @close="clearMessages" class="auth-alert">
            {{ error }}
          </n-alert>
          
          <n-alert v-if="successMessage" :title="t('app.success')" type="success" closable @close="clearMessages" class="auth-alert">
            {{ successMessage }}
          </n-alert>
        </n-form>

        <div class="register-footer">
          <p>{{ t('auth.alreadyHaveAccount') }} <router-link to="/login" class="auth-link">{{ t('auth.loginHere') }}</router-link></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, watch } from 'vue';
import axios from 'axios';
import { z } from 'zod';
import { useTranslation } from '@/composables/useTranslation';
import LanguageSelector from '@/components/LanguageSelector.vue';
import ThemeToggle from '@/components/ThemeToggle.vue';
import { useThemeStore } from '@/stores/theme'
import { storeToRefs } from 'pinia'
import apiClient from '@/services/apiClient';
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NAlert,
  NCheckbox,
  NIcon,
  NText,
  NTag,
  type FormInst,
  type FormRules,
  useMessage,
} from 'naive-ui';
import { useRouter } from 'vue-router';

const { t, isRTL } = useTranslation();
const router = useRouter();
const message = useMessage();
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// Zod schema for validation (example)
const RegisterSchema = z.object({
  email: z.string().email({ message: t('validation.emailInvalid') }),
  password: z.string().min(8, { message: t('validation.passwordInvalid', { min: 8 }) }),
  acceptTerms: z.literal(true, { errorMap: () => ({ message: t('validation.terms') }) }),
});

const formRef = ref<FormInst | null>(null);
const formValue = ref({
  email: '',
  username: '',
  password: '',
  acceptTerms: false, // Add acceptTerms to form state
});

// Local state for loading and messages
const loading = ref(false);
const error = ref<string | null>(null);
const successMessage = ref<string | null>(null);

// Username validation states
const checkingUsername = ref(false);
const usernameStatus = ref<'success' | 'error' | undefined>(undefined);
const usernameMessage = ref('');
const usernameSuggestions = ref<string[]>([]);
type TimeoutHandle = ReturnType<typeof setTimeout> | null;
const usernameTimeout = ref<TimeoutHandle>(null);
const emailTimeout   = ref<TimeoutHandle>(null);
const checkingEmail = ref(false);
const emailStatus = ref<'success' | 'error' | undefined>(undefined);
const emailMessage = ref('');

// Watch form values to clear messages on input
watch(formValue, () => {
  clearMessages();
}, { deep: true });

// Naive UI validation rules derived from Zod or defined manually
const rules: FormRules = {  email: [
    { required: true, message: t('validation.emailRequired'), trigger: 'blur' },
    {
      validator: (_rule, value) => {
        try {
          RegisterSchema.pick({ email: true }).parse({ email: value });
          return true;
        } catch (e: any) {
          return new Error(t('validation.emailInvalid'));
        }
      },
      trigger: ['input', 'blur'],    },
  ],
  username: [
    {
      validator: (_rule, value) => {
        if (!value) return true; // Username is optional
        
        if (value.length < 3) {
          return new Error(t('validation.usernameMinLength', { min: 3 }));
        }
        
        if (value.length > 20) {
          return new Error(t('validation.usernameMaxLength', { max: 20 }));
        }
        
        if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
          return new Error(t('validation.usernameInvalidChars'));
        }
        
        if (value.startsWith('_') || value.startsWith('-') || value.endsWith('_') || value.endsWith('-')) {
          return new Error(t('validation.usernameInvalidFormat'));
        }
        
        return true;
      },
      trigger: ['input', 'blur'],
    },
  ],  password: [
    { required: true, message: t('validation.passwordRequired'), trigger: 'blur' },
    {
       validator: (_rule, value) => {
        try {
          RegisterSchema.pick({ password: true }).parse({ password: value });
          return true;
        } catch (e: any) {
          return new Error(t('validation.passwordInvalid', { min: 8 }));
        }
      },
      trigger: ['input', 'blur'],
    },
  ],  acceptTerms: [
     {
        required: true,
        validator: (_rule, value) => {
            if (!value) {
                return new Error(t('validation.terms'));
            }
            return true;
        },
        trigger: ['change', 'blur'], // Trigger validation on change
     }
  ]
};

// Username validation functions
const checkUsernameAvailability = async (username: string) => {
  if (!username || username.length < 3) {
    usernameStatus.value = undefined;
    usernameMessage.value = '';
    usernameSuggestions.value = [];
    return;
  }

  checkingUsername.value = true;
  usernameStatus.value = undefined;
  usernameMessage.value = '';
  usernameSuggestions.value = [];

  try {
    const response = await apiClient.get(`/auth/username/check/${encodeURIComponent(username)}`);
    
    if (response.data.available) {
      usernameStatus.value = 'success';
      usernameMessage.value = t('auth.usernameAvailable');
    } else {
      usernameStatus.value = 'error';
      usernameMessage.value = response.data.message || t('auth.usernameTaken');
      if (response.data.suggestions) {
        usernameSuggestions.value = response.data.suggestions;
      }
    }
  } catch (error: any) {
    usernameStatus.value = 'error';
    usernameMessage.value = error.response?.data?.error || t('auth.usernameCheckError');
    if (error.response?.data?.suggestions) {
      usernameSuggestions.value = error.response.data.suggestions;
    }
  } finally {
    checkingUsername.value = false;
  }
};

const handleUsernameInput = (value: string) => {
  formValue.value.username = value;
  
  // Clear previous timeout
  if (usernameTimeout.value) {
    clearTimeout(usernameTimeout.value);
  }
  
  // Reset states immediately for UX
  usernameStatus.value = undefined;
  usernameMessage.value = '';
  usernameSuggestions.value = [];
  
  // Debounce the API call
  if (value && value.length >= 3) {
    usernameTimeout.value = setTimeout(() => {
      checkUsernameAvailability(value);
    }, 500);
  }
};

const handleUsernameBlur = () => {
  const value = formValue.value.username;
  if (value && value.length >= 3) {
    checkUsernameAvailability(value);
  }
};

const selectSuggestion = (suggestion: string) => {
  formValue.value.username = suggestion;
  usernameSuggestions.value = [];
  checkUsernameAvailability(suggestion);
};

// Email validation functions
const checkEmailAvailability = async (email: string) => {
  // Basic email validation first
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email || !emailRegex.test(email)) {
    emailStatus.value = undefined;
    emailMessage.value = '';
    return;
  }

  checkingEmail.value = true;
  emailStatus.value = undefined;
  emailMessage.value = '';

  try {
    const response = await apiClient.get(`/auth/email/check/${encodeURIComponent(email)}`);
    
    if (response.data.available) {
      emailStatus.value = 'success';
      emailMessage.value = t('auth.emailAvailable');
    } else {
      emailStatus.value = 'error';
      emailMessage.value = response.data.message || t('auth.emailTaken');
    }
  } catch (error: any) {
    emailStatus.value = 'error';
    emailMessage.value = error.response?.data?.error || t('auth.emailCheckError');
  } finally {
    checkingEmail.value = false;
  }
};

const handleEmailInput = (value: string) => {
  formValue.value.email = value;
  
  // Clear previous timeout
  if (emailTimeout.value) {
    clearTimeout(emailTimeout.value);
  }
  
  // Reset states immediately for UX
  emailStatus.value = undefined;
  emailMessage.value = '';
  
  // Debounce the API call
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (value && emailRegex.test(value)) {
    emailTimeout.value = setTimeout(() => {
      checkEmailAvailability(value);
    }, 500);
  }
};

const handleEmailBlur = () => {
  const value = formValue.value.email;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (value && emailRegex.test(value)) {
    checkEmailAvailability(value);
  }
};

const clearMessages = () => {
  error.value = null;
  successMessage.value = null;
};

const handleRegister = (e: Event) => {
  e.preventDefault();
  formRef.value?.validate()
    .then(async () => {
      loading.value = true;
      try {
        const payload = {
          email: formValue.value.email,
          password: formValue.value.password,
          username: formValue.value.username || undefined, // Include username if provided
        };
        const response = await apiClient.post('/auth/register', payload);
        // Keep the success message for potential display or logging
        successMessage.value = response.data.message || 'Registration successful! Please check your email.';
        // Optionally use Naive UI message for feedback before redirect
        message.success('Registration successful! Redirecting to login...');

        // Redirect to login page after a short delay
        setTimeout(() => {
          router.push('/login');
          // Optional: Pass a query param to show a message on the login page
          // router.push({ path: '/login', query: { registered: 'true' } });
        }, 1500); // 1.5 second delay

        // Don't clear the form immediately, let the redirect handle leaving the page
        // formValue.value = { email: '', password: '', acceptTerms: false };
      } catch (err: any) {
if (axios.isAxiosError(err) && err.response?.data) {
  const backendMsg = err.response.data.message || err.response.data.error;
  if (backendMsg) {
    error.value = backendMsg;
    message.error(backendMsg);
  } else {
    error.value = 'Registration failed. Please try again.';
    message.error('Registration failed. Please try again.');
  }
        } else if (axios.isAxiosError(err) && err.response) {
           error.value = 'Registration failed. Please try again.';
           message.error('Registration failed. Please try again.');
           console.error('Registration Axios error (no specific message):', err.response);
        } else {
          error.value = 'An unexpected error occurred. Please try again.';
          message.error('An unexpected error occurred. Please try again.');
          console.error('Registration non-Axios error:', err);
        }
      } finally {
       loading.value = false;
    }
  })
    .catch((validationErrors) => {
      // Validation failed - Show user-friendly message about validation errors
      console.log('Form validation failed:', validationErrors);
      
      // Extract validation error messages and show them to the user
      if (Array.isArray(validationErrors) && validationErrors.length > 0) {
        const firstError = validationErrors[0];
        const errorMessage = firstError?.message || 'Please check your input and try again.';
        message.error(errorMessage);
        error.value = errorMessage;
      } else {
        const errorMessage = 'Please check all required fields and try again.';
        message.error(errorMessage);
        error.value = errorMessage;
      }
      
      // Ensure loading is false if validation fails before API call
      loading.value = false;
    });
};

// Clear messages when the component is unmounted
onUnmounted(() => {
  clearMessages();
});

// Expose formRef for testing
defineExpose({ formRef });
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  position: relative;
  overflow: hidden;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-image {
  height: 60px;
  width: auto;
  transition: opacity 0.3s ease;
}

.logo:hover .logo-image {
  opacity: 0.9;
}

.card-logo-image {
  height: 120px;
  width: auto;
  margin-bottom: 1rem;
  transition: opacity 0.3s ease;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.language-selector {
  background: rgba(255, 255, 255, 0.15) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s ease !important;
}

.language-selector:hover {
  background: rgba(255, 255, 255, 0.25) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  transform: translateY(-1px) !important;
}

.signin-btn {
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Background Animation */
.register-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.25;
  animation: float 6s ease-in-out infinite;
  box-shadow: 0 4px 15px rgba(100, 108, 255, 0.2);
}

.shape-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #535bf2 0%, #646cff 100%);
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  top: 40%;
  right: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

.register-container {
  width: 100%;
  max-width: 480px;
  position: relative;
  z-index: 1;
  margin-top: 5rem; /* Account for fixed header */
}

.register-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1.5rem;
  padding: 3rem 2.5rem;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.register-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.brand-logo {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.gradient-text {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.register-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: white;
}

.register-subtitle {
  font-size: 1rem;
  color: #a0a0a0;
  margin: 0;
}

.register-form {
  margin-bottom: 2rem;
}

/* Form Item Overrides */
:deep(.n-form-item) {
  margin-bottom: 24px;
  width: 100% !important;
}

:deep(.n-form-item-blank) {
  width: 100% !important;
}

:deep(.n-form-item-label) {
  color: #d0d0d0 !important;
  font-weight: 500;
  margin-bottom: 8px;
  width: 100% !important;
}

/* Input Styling */
.auth-input {
  width: 100% !important;
}

.auth-input :deep(.n-input) {
  width: 100% !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 0.75rem !important;
  --n-color: rgba(0, 0, 0, 0.3) !important;
  --n-color-focus: rgba(0, 0, 0, 0.4) !important;
}

.auth-input :deep(.n-input:hover) {
  border-color: rgba(100, 108, 255, 0.5) !important;
  background-color: rgba(0, 0, 0, 0.4) !important;
}

.auth-input :deep(.n-input:focus-within) {
  border-color: #646cff !important;
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.2) !important;
  background-color: rgba(0, 0, 0, 0.4) !important;
}

.auth-input :deep(.n-input-wrapper) {
  width: 100% !important;
  background-color: transparent !important;
}

.auth-input :deep(.n-input__input-el) {
  color: var(--foreground-color, white) !important;
  background-color: transparent !important;
  -webkit-text-fill-color: var(--foreground-color, white) !important;
}

.auth-input :deep(.n-input__input-el::placeholder) {
  color: rgba(255, 255, 255, 0.6) !important;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.6) !important;
}

.auth-input :deep(.n-input__prefix) {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Ensure Naive UI variables are overridden */
.auth-input :deep(.n-input) {
  --n-text-color: white !important;
  --n-text-decoration-color: white !important;
  --n-caret-color: white !important;
}

/* Light Theme Styles */
[data-theme="light"] .auth-input :deep(.n-input__input-el) {
  --foreground-color: #333 !important;
}

[data-theme="light"] .auth-input :deep(.n-input__prefix) {
  color: #666 !important;
}

[data-theme="light"] .auth-input :deep(.n-input) {
  background-color: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(0, 0, 0, 0.2) !important;
}

[data-theme="light"] .auth-input :deep(.n-input:hover) {
  border-color: rgba(100, 108, 255, 0.7) !important;
  background-color: #ffffff !important;
}

[data-theme="light"] .auth-input :deep(.n-input:focus-within) {
  border-color: #646cff !important;
  background-color: #ffffff !important;
}

[data-theme="light"] .auth-input :deep(.n-input__placeholder) {
  color: #666 !important;
}

/* Terms Checkbox Styling */
.terms-item {
  margin: 1.5rem 0;
}

.terms-checkbox :deep(.n-checkbox) {
  align-items: flex-start;
}

.terms-checkbox :deep(.n-checkbox__dot) {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  margin-top: 2px;
}

.terms-checkbox :deep(.n-checkbox__dot:hover) {
  border-color: #646cff !important;
}

.terms-checkbox :deep(.n-checkbox--checked .n-checkbox__dot) {
  background-color: #646cff !important;
  border-color: #646cff !important;
}

.terms-text {
  color: #ffffff; /* Changed from #d0d0d0 to white for better visibility */
  line-height: 1.5;
  font-size: 0.875rem;
}

.terms-link {
  color: var(--primary-color, #18a058);
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

/* Submit Button */
.auth-submit-btn {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%) !important;
  border: none !important;
  border-radius: 0.75rem !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  transition: all 0.3s ease !important;
}

.auth-submit-btn:hover:not(:disabled) {
  transform: translateY(-1px) !important;
  box-shadow: 0 8px 25px rgba(100, 108, 255, 0.3) !important;
}

.auth-submit-btn:disabled {
  background: rgba(100, 108, 255, 0.3) !important;
  cursor: not-allowed !important;
}

.button-icon {
  width: 18px;
  height: 18px;
}

/* Alert Styling */
.auth-alert {
  margin-top: 1rem;
}

.auth-alert :deep(.n-alert) {
  border-radius: 0.75rem !important;
}

.auth-alert :deep(.n-alert--error) {
  background-color: rgba(255, 82, 82, 0.1) !important;
  border: 1px solid rgba(255, 82, 82, 0.3) !important;
}

.auth-alert :deep(.n-alert--success) {
  background-color: rgba(82, 255, 82, 0.1) !important;
  border: 1px solid rgba(82, 255, 82, 0.3) !important;
}

/* Footer */
.register-footer {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.register-footer p {
  margin: 0;
  color: #a0a0a0;
}

.auth-link {
  color: #646cff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #535bf2;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 0.75rem 0;
  }
  
  .logo-brand {
    display: none;
  }
  
  .header-actions {
    gap: 0.5rem;
  }
  
  .register-container {
    margin-top: 4rem;
  }
}

@media (max-width: 640px) {
  .register-card {
    padding: 2rem 1.5rem;
    margin: 1rem;
    border-radius: 1rem;
  }
  
  .brand-logo {
    font-size: 2rem;
  }
  
  .register-title {
    font-size: 1.5rem;
  }
  
  .header-actions .signin-btn {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.875rem !important;
  }
}

/* RTL Support */
.rtl .header-content {
  direction: rtl;
}

.rtl .logo {
  flex-direction: row-reverse;
}

.rtl .header-actions {
  flex-direction: row-reverse;
}

/* Light Theme Styles */
[data-theme="light"] .register-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
}

[data-theme="light"] .header {
  background: rgba(248, 250, 252, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .logo-text,
[data-theme="light"] .logo-brand {
  color: #1e293b !important;
}

[data-theme="light"] .floating-shapes .shape {
  opacity: 0.4;
}

[data-theme="light"] .shape-1 {
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.4) 0%, rgba(219, 39, 119, 0.3) 100%);
  box-shadow: 0 8px 32px rgba(236, 72, 153, 0.2);
}

[data-theme="light"] .shape-2 {
  background: linear-gradient(135deg, rgba(219, 39, 119, 0.35) 0%, rgba(100, 108, 255, 0.25) 100%);
  box-shadow: 0 8px 32px rgba(219, 39, 119, 0.15);
}

[data-theme="light"] .shape-3 {
  background: linear-gradient(135deg, rgba(100, 108, 255, 0.3) 0%, rgba(236, 72, 153, 0.35) 100%);
  box-shadow: 0 8px 32px rgba(100, 108, 255, 0.2);
}

[data-theme="light"] .register-card {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(100, 108, 255, 0.15);
  color: #1e293b;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

[data-theme="light"] .brand-logo .gradient-text {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

[data-theme="light"] .register-title {
  color: #1e293b;
}

[data-theme="light"] .register-subtitle {
  color: rgba(30, 41, 59, 0.8);
}

[data-theme="light"] .form-item {
  color: #1e293b;
}

[data-theme="light"] .terms-text {
  color: rgba(30, 41, 59, 0.8);
}

[data-theme="light"] .terms-text a {
  color: #646cff;
}

[data-theme="light"] .terms-text a:hover {
  color: #535bf2;
}

[data-theme="light"] .signin-link {
  color: rgba(30, 41, 59, 0.8);
}

[data-theme="light"] .signin-link a {
  color: #646cff;
}

[data-theme="light"] .signin-link a:hover {
  color: #535bf2;
}

[data-theme="light"] .language-selector {
  background: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

[data-theme="light"] .language-selector:hover {
  background: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(0, 0, 0, 0.2) !important;
}

/* Input validation feedback styles */
.feedback-message {
  margin-top: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  transition: all 0.3s ease;
  transform: translateY(-2px);
  opacity: 0;
  animation: fadeInUp 0.3s ease forwards;
  display: flex;
  align-items: center;
  gap: 6px;
}

.feedback-message::before {
  content: '';
  width: 4px;
  height: 4px;
  border-radius: 50%;
  flex-shrink: 0;
}

.feedback-message.success {
  color: #18a058;
  background: rgba(24, 160, 88, 0.06);
}

.feedback-message.success::before {
  background: #18a058;
}

.feedback-message.error {
  color: #d03050;
  background: rgba(208, 48, 80, 0.06);
}

.feedback-message.error::before {
  background: #d03050;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.username-suggestions {
  margin-top: 6px;
  padding: 6px 8px;
  background: rgba(24, 160, 88, 0.04);
  border-radius: 6px;
  border: 1px solid rgba(24, 160, 88, 0.1);
  animation: fadeInUp 0.3s ease forwards;
}

.suggestions-list {
  margin-top: 4px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

[data-theme="light"] .feedback-message.success {
  color: #0c7a43;
  background: rgba(12, 122, 67, 0.05);
}

[data-theme="light"] .feedback-message.error {
  color: #d03050;
  background: rgba(208, 48, 80, 0.05);
}

[data-theme="light"] .username-suggestions {
  background: rgba(12, 122, 67, 0.03);
  border-color: rgba(12, 122, 67, 0.08);
}

/* Form layout improvements */
.form-item-with-feedback {
  margin-bottom: 20px;
}

.input-wrapper {
  position: relative;
  width: 100%;
}

.input-wrapper .auth-input {
  width: 100%;
}

/* Ensure all form items have consistent width */
.form-item-with-feedback,
.register-form :deep(.n-form-item) {
  width: 100%;
}

.register-form :deep(.n-form-item .n-form-item-blank) {
  width: 100%;
}

/* Smooth transitions for feedback messages */
.feedback-slide-enter-active,
.feedback-slide-leave-active {
  transition: all 0.3s ease;
}

.feedback-slide-enter-from {
  opacity: 0;
  transform: translateY(-8px);
}

.feedback-slide-leave-to {
  opacity: 0;
  transform: translateY(-4px);
}

/* Loading state improvements */
.auth-input :deep(.n-input.n-input--loading) {
  position: relative;
}

.auth-input :deep(.n-input.n-input--loading::after) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 1.5s infinite;
  pointer-events: none;
  border-radius: inherit;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
</style>
