:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Dark theme variables */
[data-theme="dark"] {
  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
}

/* Light theme variables */
[data-theme="light"] {
  color-scheme: light;
  color: #213547;
  background-color: #ffffff;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: border-color 0.25s;
}

/* Dark theme button styles */
[data-theme="dark"] button {
  background-color: #1a1a1a;
}
[data-theme="dark"] button:hover {
  border-color: #646cff;
}

/* Light theme button styles */
[data-theme="light"] button {
  background-color: #f9f9f9;
}
[data-theme="light"] button:hover {
  border-color: #747bff;
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

/* Dark theme input styles */
[data-theme="dark"] .n-input .n-input__input-el,
[data-theme="dark"] .n-input-group .n-input .n-input__input-el,
[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="email"],
[data-theme="dark"] input[type="password"],
[data-theme="dark"] input[type="tel"],
[data-theme="dark"] input[type="number"],
[data-theme="dark"] textarea {
  color: #ffffff !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  -webkit-text-fill-color: #ffffff !important;
  -webkit-appearance: none !important;
}

[data-theme="dark"] .n-input .n-input__input-el::placeholder,
[data-theme="dark"] .n-input-group .n-input .n-input__input-el::placeholder,
[data-theme="dark"] input::placeholder,
[data-theme="dark"] textarea::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.5) !important;
}

[data-theme="dark"] .n-input,
[data-theme="dark"] .n-input-group .n-input,
[data-theme="dark"] .n-input--focus {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

[data-theme="dark"] .n-input:hover,
[data-theme="dark"] .n-input-group .n-input:hover {
  border-color: rgba(255, 255, 255, 0.3) !important;
}

[data-theme="dark"] .n-input--focus,
[data-theme="dark"] .n-input-group .n-input--focus {
  border-color: #646cff !important;
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.2) !important;
}

[data-theme="dark"] .n-input .n-input__input-el:-webkit-autofill,
[data-theme="dark"] .n-input .n-input__input-el:-webkit-autofill:hover,
[data-theme="dark"] .n-input .n-input__input-el:-webkit-autofill:focus,
[data-theme="dark"] .n-input .n-input__input-el:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px rgba(0, 0, 0, 0.3) inset !important;
  -webkit-text-fill-color: #ffffff !important;
  color: #ffffff !important;
}

/* Light theme input styles */
[data-theme="light"] .n-input .n-input__input-el,
[data-theme="light"] .n-input-group .n-input .n-input__input-el,
[data-theme="light"] input[type="text"],
[data-theme="light"] input[type="email"],
[data-theme="light"] input[type="password"],
[data-theme="light"] input[type="tel"],
[data-theme="light"] input[type="number"],
[data-theme="light"] textarea {
  color: #213547 !important;
  background-color: rgba(255, 255, 255, 0.9) !important;
  -webkit-text-fill-color: #213547 !important;
  -webkit-appearance: none !important;
}

[data-theme="light"] .n-input .n-input__input-el::placeholder,
[data-theme="light"] .n-input-group .n-input .n-input__input-el::placeholder,
[data-theme="light"] input::placeholder,
[data-theme="light"] textarea::placeholder {
  color: rgba(33, 53, 71, 0.5) !important;
  -webkit-text-fill-color: rgba(33, 53, 71, 0.5) !important;
}

[data-theme="light"] .n-input,
[data-theme="light"] .n-input-group .n-input,
[data-theme="light"] .n-input--focus {
  background-color: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(33, 53, 71, 0.2) !important;
}

[data-theme="light"] .n-input:hover,
[data-theme="light"] .n-input-group .n-input:hover {
  border-color: rgba(33, 53, 71, 0.3) !important;
}

[data-theme="light"] .n-input--focus,
[data-theme="light"] .n-input-group .n-input--focus {
  border-color: #646cff !important;
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.2) !important;
}

[data-theme="light"] .n-input .n-input__input-el:-webkit-autofill,
[data-theme="light"] .n-input .n-input__input-el:-webkit-autofill:hover,
[data-theme="light"] .n-input .n-input__input-el:-webkit-autofill:focus,
[data-theme="light"] .n-input .n-input__input-el:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px rgba(255, 255, 255, 0.9) inset !important;
  -webkit-text-fill-color: #213547 !important;
  color: #213547 !important;
}
