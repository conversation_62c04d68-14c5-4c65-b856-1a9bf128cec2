import { ref, watch } from 'vue';
import { defineStore } from 'pinia';
import apiClient from '@/services/apiClient';
import { useAuthStore } from '@/stores/auth';
import { useNotificationStore, FrontendNotificationType } from './notificationStore'; // Added FrontendNotificationType
import type { MyOffer, InterestRequestFrontend, OfferStatusFrontend, InterestStatus } from '@/types/offer';
import type {
  InterestReceivedPayload,
  InterestProcessedPayload,
  InterestRequestAcceptedAndChatReadyPayload, // NEW IMPORT
  OfferCreatedPayload,
  TransactionStatusUpdatePayload,
  PayerNegotiationStatePayload
} from '@/types/socketEvents';
import {
  INTEREST_RECEIVED, // Add INTEREST_RECEIVED event
  INTEREST_PROCESSED, // Socket event name
  INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY, // NEW IMPORT for event name
  OFFER_CREATED, // Add OFFER_CREATED event
  TRANSACTION_STATUS_UPDATED,
  NEGOTIATION_STATE_UPDATED
} from '@/types/socketEvents';
import centralizedSocketManager from '@/services/centralizedSocketManager';

// Define explicit types for the backend response structure for /offers/my
interface BackendInterest_MyOffers {
  id: string;
  interestedUserId: string;
  username: string; 
  reputationLevel: number; 
  status: string; 
  chatSessionId?: string | null;
  reasonCode?: string | null;
  createdAt: string; // ISO string for when the interest was created
  transactionStatus?: string | null; // NEW: Transaction status
  negotiationStatus?: string | null; // NEW: Negotiation status
}

interface BackendMyOffer_MyOffers {
  id: string;
  type: 'BUY' | 'SELL';
  amount: number;
  baseRate: number;
  adjustmentForLowerRep: number;
  adjustmentForHigherRep: number;
  status: string; 
  currencyPair: string;
  createdAt: string; 
  updatedAt?: string; 
  user: {
    username: string;
    reputationLevel: number;
  };
  interests?: BackendInterest_MyOffers[];
}

export const useMyOffersStore = defineStore('myOffers', () => {
  console.log('🔥 [myOffersStore] Store is being created/initialized');
  const myOffers = ref<MyOffer[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const message = ref<string | null>(null);
  const showDeclineModal = ref(false);
  const declineReasonCode = ref<string | undefined>(undefined);
  const interestToDecline = ref<InterestRequestFrontend | null>(null);
  const mapBackendMyOfferToFrontend = (backendOffer: BackendMyOffer_MyOffers): MyOffer => {
    const mappedInterests: InterestRequestFrontend[] = backendOffer.interests
      ? backendOffer.interests.map(i => ({
          id: i.id,
          offerId: backendOffer.id, 
          interestedUserId: i.interestedUserId,
          username: i.username || 'Unknown User',
          reputationLevel: i.reputationLevel ?? 1,
          status: i.status as InterestStatus, 
          chatSessionId: i.chatSessionId, // Already allows null via InterestRequestFrontend type update
          reasonCode: i.reasonCode,
          createdAt: i.createdAt,
          transactionStatus: i.transactionStatus || null, // NEW: Include transaction status
          negotiationStatus: i.negotiationStatus || null, // NEW: Include negotiation status
        }))
      : [];

    return {
      id: backendOffer.id,
      type: backendOffer.type,
      amount: backendOffer.amount,
      baseRate: backendOffer.baseRate,
      adjustmentForLowerRep: backendOffer.adjustmentForLowerRep,
      adjustmentForHigherRep: backendOffer.adjustmentForHigherRep,
      status: backendOffer.status as OfferStatusFrontend, 
      currencyPair: backendOffer.currencyPair,
      createdAt: backendOffer.createdAt,
      updatedAt: backendOffer.updatedAt,
      user: backendOffer.user ? { username: backendOffer.user.username, reputationLevel: backendOffer.user.reputationLevel } : { username: 'You', reputationLevel: 3 },
      interests: mappedInterests,
    };
  };

  async function fetchMyOffers() {
    loading.value = true;
    error.value = null;
    message.value = null;
    try {
      console.log('[myOffersStore] Fetching my offers with includeInterests=1');
      const response = await apiClient.get<BackendMyOffer_MyOffers[]>('/offers/my?includeInterests=1');
      console.log('[myOffersStore] Received response for /offers/my:', response.data);
      
      if (response.data && Array.isArray(response.data)) {
        myOffers.value = response.data.map(mapBackendMyOfferToFrontend);
        console.log('[myOffersStore] Mapped myOffers:', JSON.parse(JSON.stringify(myOffers.value)));
      } else {
        console.error('[myOffersStore] Invalid data received from /offers/my, expected an array:', response.data);
        myOffers.value = [];
        error.value = 'Received invalid data for your offers.';
      }
    } catch (err: any) {
      console.error('[myOffersStore] Error fetching my offers:', err);
      error.value = err.response?.data?.message || err.message || 'Failed to fetch offers';
      myOffers.value = [];
    } finally {
      loading.value = false;
    }
  }

  async function acceptInterest(interestId: string /* offerId: string - unused */) {
    loading.value = true;
    error.value = null;
    message.value = null;
    try {
      const response = await apiClient.post(`/offers/interests/${interestId}/accept`);
      message.value = response.data.message;
      console.log('[myOffersStore] Interest accepted via API, waiting for socket event', response.data);
    } catch (err: any) {
      console.error('[myOffersStore] Error accepting interest:', err);
      error.value = err.response?.data?.message || 'Failed to accept interest';
    }
    loading.value = false;
  }

  function openDeclineInterestModal(interest: InterestRequestFrontend) {
    interestToDecline.value = interest;
    declineReasonCode.value = undefined; 
    showDeclineModal.value = true;
  }

  async function handleDeclineReasonSubmit() {
    if (!interestToDecline.value || !interestToDecline.value.id) {
      console.error('[myOffersStore] No interest selected for decline.');
      error.value = 'No interest selected for decline.';
      return;
    }
    showDeclineModal.value = false;
    await declineInterest(interestToDecline.value.id, declineReasonCode.value);
    interestToDecline.value = null;
    declineReasonCode.value = undefined;
  }

  async function declineInterest(interestId: string, reasonCode?: string /* offerId: string - unused */) {
    loading.value = true;
    error.value = null;
    message.value = null;
    try {
      const payload: { reasonCode?: string } = {};
      if (reasonCode) {
        payload.reasonCode = reasonCode;
      }
      const response = await apiClient.post(`/offers/interests/${interestId}/decline`, payload);
      message.value = response.data.message;
      console.log('[myOffersStore] Interest declined via API, waiting for socket event', response.data);
    } catch (err: any) {
      console.error('[myOffersStore] Error declining interest:', err);
      error.value = err.response?.data?.message || 'Failed to decline interest';
    }
    loading.value = false;
  }  // Helper function to process interest for a specific offer
  function processInterestForOffer(payload: InterestReceivedPayload, offerIndex: number, contextLabel: string = '') {
    const offer = myOffers.value[offerIndex];
    const existingInterestIndex = offer.interests.findIndex(i => i.id === payload.interestId);
    
    // Ensure newInterest conforms to InterestRequestFrontend type
    const newInterest: InterestRequestFrontend = {
      id: payload.interestId,
      offerId: payload.offerId,
      interestedUserId: payload.interestedUserId,
      username: payload.interestedUser.username,
      reputationLevel: payload.interestedUser.reputationLevel,
      status: 'PENDING', // payload.status should be 'PENDING'
      createdAt: payload.createdAt,
      // chatSessionId and reasonCode are typically null/undefined for new PENDING interest
    };

    let updatedInterests;
    if (existingInterestIndex === -1) {
      updatedInterests = [newInterest, ...offer.interests]; // Add to the beginning
      console.log(`[myOffersStore] New interest added to offer${contextLabel}:`, payload.offerId, newInterest);
    } else {
      // Replace existing interest to ensure all details are fresh
      updatedInterests = [...offer.interests];
      updatedInterests[existingInterestIndex] = { 
          ...offer.interests[existingInterestIndex], // Keep existing fields like chatSessionId if any
          ...newInterest // Overwrite with new data
      };
      console.warn(`[myOffersStore] Interest already existed${contextLabel}, updated it with new payload details.`, payload.interestId);
    }
    
    // Create a new offer object with the updated interests array for reactivity
    myOffers.value[offerIndex] = {
      ...offer,
      interests: updatedInterests,
    };  }

  // NEW: Handle transaction status updates
  function handleTransactionStatusUpdate(payload: TransactionStatusUpdatePayload) {
    console.log('🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event:', payload);
    
    // Find the offer that contains this transaction
    const offerIndex = myOffers.value.findIndex(offer => 
      offer.interests.some(interest => interest.chatSessionId && 
        // The payload contains chatSessionId, we need to match it with interests that have this chatSessionId
        interest.chatSessionId === payload.chatSessionId
      )
    );

    if (offerIndex !== -1) {
      const offer = myOffers.value[offerIndex];
      const interestIndex = offer.interests.findIndex(interest => 
        interest.chatSessionId === payload.chatSessionId
      );

      if (interestIndex !== -1) {
        console.log(`[myOffersStore] Updating transaction status for interest ${offer.interests[interestIndex].id} to ${payload.status}`);
        
        // Update the interest with new transaction status
        const updatedInterests = [...offer.interests];
        updatedInterests[interestIndex] = {
          ...updatedInterests[interestIndex],
          transactionStatus: payload.status
        };

        // Update the offer in the store
        myOffers.value[offerIndex] = {
          ...offer,
          interests: updatedInterests,
        };
      } else {
        console.warn('[myOffersStore] Interest with matching chatSessionId not found for transaction update:', payload.chatSessionId);
      }
    } else {
      console.warn('[myOffersStore] Offer not found for transaction status update:', payload);
    }
  }

  // NEW: Handle negotiation state updates
  function handleNegotiationStateUpdate(payload: PayerNegotiationStatePayload) {
    console.log('🔥 [myOffersStore] Handling NEGOTIATION_STATE_UPDATED event:', payload);
    
    // Find the offer that contains this negotiation
    const offerIndex = myOffers.value.findIndex(offer => 
      offer.interests.some(interest => interest.chatSessionId && 
        // The payload contains transactionId, we need to find interests with matching transaction
        interest.transactionStatus // This indicates the interest has a transaction
      )
    );

    if (offerIndex !== -1) {
      const offer = myOffers.value[offerIndex];
      // For negotiation updates, we need to find the interest that matches this transaction
      // Since we don't have a direct link, we'll update all interests that have transaction status
      // This is a limitation of the current data structure - in practice, you might want to 
      // include more identifying information in the payload
      
      const updatedInterests = offer.interests.map(interest => {
        // Update negotiation status for interests that have transaction status (indicating they're in the transaction flow)
        if (interest.transactionStatus) {
          return {
            ...interest,
            negotiationStatus: payload.negotiationStatus
          };
        }
        return interest;
      });

      // Update the offer in the store
      myOffers.value[offerIndex] = {
        ...offer,
        interests: updatedInterests,
      };
      
      console.log(`[myOffersStore] Updated negotiation status for offer ${offer.id} interests to ${payload.negotiationStatus}`);
    } else {
      console.warn('[myOffersStore] Offer not found for negotiation state update:', payload);
    }
  }

  async function handleInterestReceived(payload: InterestReceivedPayload) {
    console.log('🔥🔥🔥 [myOffersStore] Handling INTEREST_RECEIVED event:', payload);
    const offerIndex = myOffers.value.findIndex(offer => offer.id === payload.offerId);
    
    if (offerIndex !== -1) {
      processInterestForOffer(payload, offerIndex);
    } else {
      console.warn(
        '[myOffersStore] INTEREST_RECEIVED: Offer not found in local store. Offer ID from payload:', payload.offerId,
        'Current offer IDs in store:', myOffers.value.map(o => o.id)
      );
      // If the offer is not found, it might be a newly created offer that hasn't been fetched yet
      // Let's re-fetch the offers to get the latest data
      console.log('[myOffersStore] Re-fetching offers to include the missing offer');
      await fetchMyOffers();
      
      // Try to handle the interest again after re-fetching
      const offerIndexAfterRefetch = myOffers.value.findIndex(offer => offer.id === payload.offerId);
      if (offerIndexAfterRefetch !== -1) {
        console.log('[myOffersStore] Offer found after re-fetch, processing interest');
        processInterestForOffer(payload, offerIndexAfterRefetch, ' after re-fetch');
      } else {
        console.error('[myOffersStore] Offer still not found after re-fetch. This might indicate a data consistency issue.');
      }
    }
    
    // REMOVED: notificationStore.addInterestNotification(payload);
    // The notificationStore listens to NEW_NOTIFICATION directly from the backend service.
  }

  function handleOfferCreated(payload: OfferCreatedPayload) {
    console.log('🔥 [myOffersStore] Handling OFFER_CREATED event:', payload);
    
    // Check if this offer was created by the current user
    const authStore = useAuthStore();
    const currentUser = authStore.user;
    
    if (!currentUser) {
      console.log('[myOffersStore] No current user, ignoring OFFER_CREATED event');
      return;
    }    // Check if we need to re-fetch offers
    // Case 1: Payload includes userId and it matches the current user
    // Case 2: Payload doesn't include userId, so we'll fetch anyway to ensure we don't miss offers
    if (payload.offerId && (payload.userId === currentUser.id || !payload.userId)) {
      console.log('[myOffersStore] New offer created, re-fetching offers to include it');
      
      // If we have the full offer data in the payload, add it immediately to the local state
      if (payload.fullOfferData) {
        console.log('[myOffersStore] Adding new offer directly to local state:', payload.fullOfferData);
        try {
          const mappedOffer = mapBackendMyOfferToFrontend(payload.fullOfferData);
          myOffers.value.unshift(mappedOffer); // Add to the beginning of the array
          console.log('[myOffersStore] New offer added to local state');
        } catch (error) {
          console.error('[myOffersStore] Error mapping offer data to frontend model:', error);
        }
      }
      
      // Re-fetch offers to get the complete offer data
      fetchMyOffers().then(() => {
        console.log('🔥 [myOffersStore] Successfully re-fetched offers after OFFER_CREATED');
      }).catch((error) => {
        console.error('[myOffersStore] Failed to re-fetch offers after OFFER_CREATED:', error);
      });
    }
  }

  function handleInterestProcessed(payload: InterestProcessedPayload) {
    console.log('[myOffersStore] Handling INTEREST_PROCESSED event:', payload);
    const offerIndex = myOffers.value.findIndex(o => o.id === payload.offerId);

    if (offerIndex !== -1) {
      const offer = myOffers.value[offerIndex];
      const interestIndex = offer.interests.findIndex(i => i.id === payload.interestId);

      if (interestIndex !== -1) {
        const originalInterest = offer.interests[interestIndex];
        const updatedInterest: InterestRequestFrontend = {
          ...originalInterest,
          status: payload.newStatus as InterestStatus, // Ensure status is updated
        };

        if (payload.newStatus === 'DECLINED') {
          updatedInterest.reasonCode = payload.reasonCode;
        }
        // Although INTEREST_PROCESSED from offer.ts is typically for declines,
        // being thorough with chatSessionId if status were ACCEPTED.
        if (payload.newStatus === 'ACCEPTED') {
          updatedInterest.chatSessionId = payload.chatSessionId;
        }

        // Create a new array for the interests to ensure reactivity for the offer object
        const updatedInterests = [
          ...offer.interests.slice(0, interestIndex),
          updatedInterest,
          ...offer.interests.slice(interestIndex + 1),
        ];
        
        // Create a new offer object with the updated interests array
        myOffers.value[offerIndex] = {
          ...offer,
          interests: updatedInterests,
        };

        console.log('[myOffersStore] Interest status updated (by replacement) in offer:', payload.offerId, updatedInterest);

        // Remove the corresponding NEW_INTEREST_ON_YOUR_OFFER notification
        // and mark it as read on the backend.
        const notificationStore = useNotificationStore();
        const notificationToRemove = notificationStore.notifications.find(n => 
          n.type === FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER && 
          n.data?.interestId === payload.interestId
        );

        if (notificationToRemove) {
          console.log(`[myOffersStore] Found notification ${notificationToRemove.id} to process for interest ${payload.interestId}`);
          // Mark as read on the backend first
          notificationStore.markNotificationAsRead(notificationToRemove.id)
            .then(() => {
              console.log(`[myOffersStore] Successfully marked notification ${notificationToRemove.id} as read on backend.`);
              // Then remove locally for immediate UI update
              notificationStore.removeNotificationById(notificationToRemove.id);
              console.log(`[myOffersStore] Removed notification ${notificationToRemove.id} locally.`);
            })
            .catch(error => {
              console.error(`[myOffersStore] Failed to mark notification ${notificationToRemove.id} as read on backend:`, error);
              // Still remove locally if backend update fails, to maintain UI consistency for the session
              notificationStore.removeNotificationById(notificationToRemove.id);
              console.warn(`[myOffersStore] Removed notification ${notificationToRemove.id} locally despite backend error.`);
            });
        } else {
          console.warn(`[myOffersStore] No NEW_INTEREST_ON_YOUR_OFFER notification found for processed interest ${payload.interestId} to remove/mark as read.`);
        }

      } else {
        console.warn('[myOffersStore] Processed interest not found in offer\'s interests list:', payload.interestId, 'for offer:', payload.offerId);
      }
    } else {
      console.warn('[myOffersStore] Offer not found for processed interest:', payload.offerId);
    }
  }  // NEW HANDLER for when an interest is accepted and chat is ready
  function handleInterestAcceptedAndChatReady(payload: InterestRequestAcceptedAndChatReadyPayload) {
    console.log('🔥 [myOffersStore] HANDLER CALLED - INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY event:', payload);
    const authStore = useAuthStore();
    const currentUser = authStore.user;
    
    // Verify this event is for the current user as the offer creator
    if (!currentUser || !payload.offerCreator || payload.offerCreator.userId !== currentUser.id) {
      console.log('[myOffersStore] Event not for current user as offer creator, ignoring:', { 
        currentUserId: currentUser?.id, 
        payloadOfferCreatorId: payload.offerCreator?.userId 
      });
      return;
    }
    
    const offerIndex = myOffers.value.findIndex(o => o.id === payload.offerId); // Use top-level payload.offerId

    if (offerIndex !== -1) {
      const offer = myOffers.value[offerIndex];
      const interestIndex = offer.interests.findIndex(i => i.id === payload.interestId);

      if (interestIndex !== -1) {
        // Create a new object for the updated interest to ensure reactivity
        const updatedInterest: InterestRequestFrontend = {
          ...offer.interests[interestIndex],
          status: 'ACCEPTED',
          chatSessionId: payload.chatSessionId,
        };

        // Create a new array for the interests to ensure reactivity for the offer object
        const updatedInterests = [
          ...offer.interests.slice(0, interestIndex),
          updatedInterest,
          ...offer.interests.slice(interestIndex + 1),
        ];
        
        // Create a new offer object with the updated interests array
        myOffers.value[offerIndex] = {
          ...offer,
          interests: updatedInterests,
        };

        console.log('[myOffersStore] Interest status updated (by replacement) to ACCEPTED and chatSessionId set in offer:', payload.offerId, updatedInterest);

        // Remove the corresponding NEW_INTEREST_ON_YOUR_OFFER notification
        const notificationStore = useNotificationStore();
        const notificationToRemove = notificationStore.notifications.find(n =>
          n.type === FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER &&
          n.data?.interestId === payload.interestId
        );

        if (notificationToRemove) {
          console.log(`[myOffersStore] Found notification ${notificationToRemove.id} to process for accepted interest ${payload.interestId}`);
          notificationStore.markNotificationAsRead(notificationToRemove.id)
            .then(() => {
              console.log(`[myOffersStore] Successfully marked notification ${notificationToRemove.id} as read on backend.`);
              notificationStore.removeNotificationById(notificationToRemove.id);
              console.log(`[myOffersStore] Removed notification ${notificationToRemove.id} locally.`);
            })
            .catch(error => {
              console.error(`[myOffersStore] Failed to mark notification ${notificationToRemove.id} as read on backend:`, error);
              notificationStore.removeNotificationById(notificationToRemove.id);
              console.warn(`[myOffersStore] Removed notification ${notificationToRemove.id} locally despite backend error.`);
            });
        } else {
          console.warn(`[myOffersStore] No NEW_INTEREST_ON_YOUR_OFFER notification found for accepted interest ${payload.interestId} to remove/mark as read.`);
        }
      } else {
        console.warn('[myOffersStore] Accepted interest not found in offer\'s interests list:', payload.interestId, 'for offer:', payload.offerId); // Use payload.offerId
      }
    } else {
      console.warn('[myOffersStore] Offer not found for accepted interest:', payload.offerId); // Use payload.offerId
    }
  }
  const authStore = useAuthStore();  // Store the unsubscribe functions for centralized socket manager
  let interestReceivedUnsubscribe: (() => void) | null = null;
  let interestProcessedUnsubscribe: (() => void) | null = null;
  let interestAcceptedUnsubscribe: (() => void) | null = null;
  let offerCreatedUnsubscribe: (() => void) | null = null;
  let transactionStatusUnsubscribe: (() => void) | null = null;
  let negotiationStateUnsubscribe: (() => void) | null = null;    function registerSocketEventHandlers() {
    console.log(`🔥 [myOffersStore] Registering handlers with centralized socket manager`);
    
    // Clean up previous handlers if they exist
    if (interestReceivedUnsubscribe) {
      interestReceivedUnsubscribe();
      interestReceivedUnsubscribe = null;
    }
    if (interestProcessedUnsubscribe) {
      interestProcessedUnsubscribe();
      interestProcessedUnsubscribe = null;
    }
    if (interestAcceptedUnsubscribe) {
      interestAcceptedUnsubscribe();
      interestAcceptedUnsubscribe = null;
    }
    if (offerCreatedUnsubscribe) {
      offerCreatedUnsubscribe();
      offerCreatedUnsubscribe = null;
    }
    if (transactionStatusUnsubscribe) {
      transactionStatusUnsubscribe();
      transactionStatusUnsubscribe = null;
    }
    if (negotiationStateUnsubscribe) {
      negotiationStateUnsubscribe();
      negotiationStateUnsubscribe = null;
    }
    
    // Register handlers with centralized socket manager
    interestReceivedUnsubscribe = centralizedSocketManager.on(INTEREST_RECEIVED, handleInterestReceived);
    interestProcessedUnsubscribe = centralizedSocketManager.on(INTEREST_PROCESSED, handleInterestProcessed);
    interestAcceptedUnsubscribe = centralizedSocketManager.on(INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY, handleInterestAcceptedAndChatReady);
    offerCreatedUnsubscribe = centralizedSocketManager.on(OFFER_CREATED, handleOfferCreated);
    transactionStatusUnsubscribe = centralizedSocketManager.on(TRANSACTION_STATUS_UPDATED, handleTransactionStatusUpdate);
    negotiationStateUnsubscribe = centralizedSocketManager.on(NEGOTIATION_STATE_UPDATED, handleNegotiationStateUpdate);
    
    console.log('🔥 [myOffersStore] All handlers registered with centralized socket manager');
  }  function unregisterSocketEventHandlers() {
    console.log('[myOffersStore] Unregistering handlers from centralized socket manager');
    
    // Clean up subscriptions
    if (interestReceivedUnsubscribe) {
      interestReceivedUnsubscribe();
      interestReceivedUnsubscribe = null;
    }
    if (interestProcessedUnsubscribe) {
      interestProcessedUnsubscribe();
      interestProcessedUnsubscribe = null;
    }
    if (interestAcceptedUnsubscribe) {
      interestAcceptedUnsubscribe();
      interestAcceptedUnsubscribe = null;
    }
    if (offerCreatedUnsubscribe) {
      offerCreatedUnsubscribe();
      offerCreatedUnsubscribe = null;
    }
    if (transactionStatusUnsubscribe) {
      transactionStatusUnsubscribe();
      transactionStatusUnsubscribe = null;
    }
    if (negotiationStateUnsubscribe) {
      negotiationStateUnsubscribe();
      negotiationStateUnsubscribe = null;
    }
  }
  function setupAuthAndSocketListeners() {
    console.log('🔥 [myOffersStore] setupAuthAndSocketListeners called');

    watch(
      () => authStore.isAuthenticated,
      async (isAuth, wasAuth) => {
        if (isAuth) {
          console.log('[myOffersStore] User authenticated.');
          try {
            // Ensure socket is available before proceeding
            const maxRetries = 10;
            let retries = 0;
            while (!centralizedSocketManager.getSocket() && retries < maxRetries) {
              await new Promise(resolve => setTimeout(resolve, 50));
              retries++;
            }
            
            if (!centralizedSocketManager.getSocket()) {
              throw new Error('Socket manager failed to initialize');
            }
            
            console.log('🔥 [myOffersStore] Fetching offers and registering event handlers.');
            await fetchMyOffers(); // Fetch offers first
            
            // Register handlers with centralized socket manager
            registerSocketEventHandlers();
            console.log('🔥 [myOffersStore] Event handlers registered successfully.');
            
          } catch (e: any) {
            console.error('[myOffersStore] Critical error during initialization:', e.message, e);
            error.value = 'Initialization failed. Real-time features may be unavailable.';
          }
        } else if (wasAuth && !isAuth) { // User logged out
          console.log('[myOffersStore] User logged out.');
          myOffers.value = [];
          unregisterSocketEventHandlers();
        }
      },
      { immediate: true }
    );
  }

  console.log('🔥 [myOffersStore] About to call setupAuthAndSocketListeners');
  setupAuthAndSocketListeners();

  return {
    myOffers,
    loading,
    error,
    message,
    fetchMyOffers,
    acceptInterest,
    declineInterest,
    handleDeclineReasonSubmit,
    showDeclineModal,
    declineReasonCode,
    interestToDecline,
    openDeclineInterestModal
  };
});
