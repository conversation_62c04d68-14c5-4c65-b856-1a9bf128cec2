import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import { createPinia } from 'pinia';
import { useAuthStore } from './stores/auth'; // Import useAuthStore
import { useLanguageStore } from './stores/language'; // Import useLanguageStore
import { useClientLogger } from './composables/useClientLogger'; // Import client logger
import naive from 'naive-ui';
import { i18n } from './i18n'; // Import named i18n export
import type { Composer } from 'vue-i18n';
import type { MessageSchema } from './i18n';

const app = createApp(App);
const pinia = createPinia();
app.use(pinia);

// Initialize client logger and set up global error handling
const logger = useClientLogger();

// Vue component error handling
app.config.errorHandler = (err, instance, info) => {
  logger.handleVueError(err, instance, info);
};

// Global JavaScript error handling
window.onerror = (message, source, lineno, colno, error) => {
  logger.handleGlobalError(message, source, lineno, colno, error);
  // Implicit return undefined allows default browser error handling consistently
};

// Unhandled promise rejection handling
window.onunhandledrejection = (event) => {
  logger.handleUnhandledRejection(event);
  // Implicit return undefined allows default browser error handling consistently
};

// Log application startup
logger.logInfo('app' as 'app', {
  message: 'MUNygo application starting',
  userAgent: navigator.userAgent,
  url: window.location.href,
  timestamp: new Date().toISOString(),
});

// Initialize auth store and attempt to load auth state from localStorage
const authStore = useAuthStore();
authStore.initializeAuth();

// Initialize language store and set default language to Persian
const languageStore = useLanguageStore();
languageStore.initializeLanguage();

// Initialize i18n and sync with language store
app.use(i18n);
const i18nGlobal = i18n.global as Composer<MessageSchema>;
i18nGlobal.locale.value = languageStore.currentLanguage;

// Initialize socket connection if user is already authenticated
// This ensures the socket connects automatically on page refresh/app restart
if (authStore.isAuthenticated) {
  console.log('🔌 [Main] User is authenticated, initializing socket connection...');
  logger.logInfo('auth' as 'auth', {
    message: 'Socket connection initializing for authenticated user',
    userId: authStore.user?.id,
  });
  authStore.initializeSocketConnection();
}

app.use(router);
app.use(naive); // Use Naive UI

// Load connection testing utilities in development
if (import.meta.env.DEV) {
  import('./utils/connectionTester');
}

// Log successful app mount
logger.logInfo('app' as 'app', { message: 'MUNygo application mounted successfully' });

app.mount('#app');
