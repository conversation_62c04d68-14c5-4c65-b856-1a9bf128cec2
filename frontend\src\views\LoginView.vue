<template>
  <div class="login-page" :class="{ 'rtl': isRTL, 'ltr': !isRTL }" :dir="isRTL ? 'rtl' : 'ltr'">
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="header-content">          <router-link to="/" class="logo">
            <img 
              :src="isDark ? '/logo-dark.webp' : '/logo-light.webp'" 
              :alt="t('landing.heroTitle')"
              class="logo-image"
            />
          </router-link>
            <div class="header-actions">
            <ThemeToggle class="theme-toggle" />
            <LanguageSelector class="language-selector" />
            <router-link to="/register">
              <n-button type="primary" class="signup-btn">{{ t('landing.joinNow') }}</n-button>
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <!-- Background Animation -->
    <div class="login-background">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
    </div>

    <div class="login-container">
      <div class="login-card">        <div class="login-header">          <div class="brand-logo">
            <img 
              :src="isDark ? '/logo-dark.webp' : '/logo-light.webp'" 
              :alt="t('landing.heroTitle')"
              class="card-logo-image"
            />
          </div>
          <h1 class="login-title">{{ t('auth.signIn') }}</h1>
          <p class="login-subtitle">{{ t('auth.welcomeBack') }}</p>
        </div>

        <n-form ref="formRef" :model="formValue" :rules="rules" @submit.prevent="handleLogin" class="login-form">          <n-form-item path="email" :label="t('auth.email')">
            <n-input 
              v-model:value="formValue.email" 
              :placeholder="t('auth.enterEmail')" 
              size="large"
              class="auth-input"
              :input-props="{ autocomplete: 'email' }"
            >
              <template #prefix>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/>
                  </svg>
                </n-icon>
              </template>
            </n-input>
          </n-form-item>          <n-form-item path="password" :label="t('auth.password')">
            <n-input
              v-model:value="formValue.password"
              type="password"
              show-password-on="click"
              :placeholder="t('auth.enterPassword')"
              size="large"
              class="auth-input"
              :input-props="{ autocomplete: 'current-password' }"
            >
              <template #prefix>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"/>
                  </svg>
                </n-icon>
              </template>
            </n-input>
          </n-form-item>
          
          <n-form-item>
            <n-button 
              type="primary" 
              attr-type="submit" 
              :loading="loading" 
              block 
              size="large"
              class="auth-submit-btn"
            >
              <n-icon class="button-icon" v-if="!loading">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M10,17L5,12L6.41,10.58L10,14.17L17.59,6.58L19,8M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                </svg>
              </n-icon>              {{ t('auth.signIn') }}
            </n-button>
          </n-form-item>          <n-alert v-if="error" :title="t('errors.login')" type="error" closable @close="error = null" class="auth-alert">
            {{ error }}
          </n-alert>

          <!-- Resend Verification Section -->
          <div v-if="needsVerification && !verificationSent" class="resend-verification-section">
            <n-alert type="info" :show-icon="false" class="verification-info">
              <div class="verification-content">
                <p>{{ t('auth.emailNotVerified') }}</p>
                <n-button 
                  type="primary" 
                  size="small" 
                  :loading="resendingVerification"
                  @click="handleResendVerification"
                  class="resend-btn"
                >
                  {{ t('auth.resendVerification') }}
                </n-button>
              </div>
            </n-alert>
          </div>

          <div v-if="verificationSent" class="verification-sent-section">
            <n-alert type="success" :show-icon="true" class="verification-sent">
              {{ t('auth.verificationEmailSent') }}
            </n-alert>
          </div>
        </n-form>

        <div class="login-footer">
          <p>{{ t('auth.noAccount') }} <router-link to="/register" class="auth-link">{{ t('auth.registerHere') }}</router-link></p>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import { useAuthStore } from '@/stores/auth';
import { z } from 'zod';
import { useTranslation } from '@/composables/useTranslation';
import LanguageSelector from '@/components/LanguageSelector.vue';
import ThemeToggle from '@/components/ThemeToggle.vue';
import { useThemeStore } from '@/stores/theme'
import { storeToRefs } from 'pinia'
import { authService } from '@/services/authService';
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NAlert,
  NIcon,
  useMessage,
  type FormInst,
  type FormRules,
} from 'naive-ui';

const { t, isRTL } = useTranslation();
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// --- Define Zod Schema ---
const LoginSchema = z.object({
  email: z.string().email({ message: 'Please input a valid email' }),
  password: z.string().min(1, { message: 'Password cannot be empty' }), // Or add min length if desired
});
// --- End Zod Schema ---


const formRef = ref<FormInst | null>(null);
const formValue = ref({
  email: '',
  password: '',
});
const loading = ref(false);
const error = ref<string | null>(null);
const needsVerification = ref(false);
const resendingVerification = ref(false);
const verificationSent = ref(false);
const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const message = useMessage();

// Show session expired message if present in query
onMounted(() => {
  if (route.query.message && typeof route.query.message === 'string') {
    message.warning(route.query.message);
    // Optionally clear the query param after showing
    router.replace({ query: { ...route.query, message: undefined } });
  }
});

// --- Update Naive UI rules to use Zod ---
const rules: FormRules = {
  email: [
    { required: true, message: t('validation.emailRequired'), trigger: 'blur' },
    {
      validator: (_rule, value) => {
        // Only validate format if there's a value (required rule handles empty case)
        if (!value || value.trim() === '') {
          return true;
        }
        try {
          LoginSchema.pick({ email: true }).parse({ email: value });
          return true;
        } catch (e: any) {
          return new Error(t('validation.emailInvalid'));
        }
      },
      trigger: ['input', 'blur'],
    },
  ],  password: [
    { required: true, message: t('validation.passwordRequired'), trigger: 'blur' },
    {
      validator: (_rule, value) => {
        // Only validate format if there's a value (required rule handles empty case)
        if (!value || value.trim() === '') {
          return true;
        }
        try {
          LoginSchema.pick({ password: true }).parse({ password: value });
          return true;
        } catch (e: any) {
          return new Error(e.errors[0]?.message || 'Invalid password');
        }
      },
      trigger: ['input', 'blur'],
    },
  ],
};
// --- End Update Naive UI rules ---

const handleLogin = async () => {
  error.value = null;
  needsVerification.value = false;
  verificationSent.value = false;

  try {
    await formRef.value?.validate();
    console.log('Validation passed');
  } catch (validationErrors) {
    console.log('Validation failed:', validationErrors);
    // Naive UI will display errors based on the rules
    return;
  }

  loading.value = true;

  try {
    const response = await axios.post('/api/auth/login', formValue.value);
    authStore.login(response.data.token, response.data.user);
    router.push('/');
  } catch (err: any) {
    console.error('Login failed:', err);
    if (axios.isAxiosError(err)) {
      const errorMessage = err.response?.data?.error || 'An unknown login error occurred.';
      const isVerificationError = err.response?.status === 403 && 
        (errorMessage.toLowerCase().includes('verify') || err.response?.data?.needsVerification);
      
      if (isVerificationError) {
        needsVerification.value = true;
        error.value = errorMessage;
      } else {
        error.value = errorMessage;
      }
    } else {
      error.value = 'An unknown login error occurred.';
    }
  } finally {
    loading.value = false;
  }
};

const handleResendVerification = async () => {
  if (!formValue.value.email) {
    message.error('Please enter your email address first');
    return;
  }

  resendingVerification.value = true;

  try {
    await authService.resendVerificationEmail(formValue.value.email);
    verificationSent.value = true;
    message.success('Verification email sent! Please check your inbox.');
  } catch (err: any) {
    console.error('Resend verification failed:', err);
    if (axios.isAxiosError(err)) {
      message.error(err.response?.data?.error || 'Failed to send verification email');
    } else {
      message.error('Failed to send verification email');
    }
  } finally {
    resendingVerification.value = false;
  }
};

defineExpose({ formRef });
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  position: relative;
  overflow: hidden;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-image {
  height: 60px;
  width: auto;
  transition: opacity 0.3s ease;
}

.logo:hover .logo-image {
  opacity: 0.9;
}

.card-logo-image {
  height: 120px;
  width: auto;
  margin-bottom: 1rem;
  transition: opacity 0.3s ease;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.logo-brand {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.language-selector {
  background: rgba(255, 255, 255, 0.15) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s ease !important;
}

.language-selector:hover {
  background: rgba(255, 255, 255, 0.25) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  transform: translateY(-1px) !important;
}

.signup-btn {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%) !important;
  border: none !important;
}

/* Background Animation */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.25;
  animation: float 6s ease-in-out infinite;
  box-shadow: 0 4px 15px rgba(100, 108, 255, 0.2);
}

.shape-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #535bf2 0%, #646cff 100%);
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  top: 40%;
  right: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

.login-container {
  width: 100%;
  max-width: 480px;
  position: relative;
  z-index: 1;
  margin-top: 5rem; /* Account for fixed header */
}

.login-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1.5rem;
  padding: 3rem 2.5rem;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.login-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.brand-logo {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.gradient-text {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: white;
}

.login-subtitle {
  font-size: 1rem;
  color: #a0a0a0;
  margin: 0;
}

.login-form {
  margin-bottom: 2rem;
}

/* Form Item Overrides */
:deep(.n-form-item-label) {
  color: #d0d0d0 !important;
  font-weight: 500;
}

/* Input Styling */
.auth-input :deep(.n-input) {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 0.75rem !important;
  --n-color: rgba(0, 0, 0, 0.3) !important;
  --n-color-focus: rgba(0, 0, 0, 0.4) !important;
}

.auth-input :deep(.n-input:hover) {
  border-color: rgba(100, 108, 255, 0.5) !important;
  background-color: rgba(0, 0, 0, 0.4) !important;
}

.auth-input :deep(.n-input:focus-within) {
  border-color: #646cff !important;
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.2) !important;
  background-color: rgba(0, 0, 0, 0.4) !important;
}

.auth-input :deep(.n-input-wrapper) {
  background-color: transparent !important;
}

.auth-input :deep(.n-input__input-el) {
  color: var(--foreground-color, white) !important;
  background-color: transparent !important;
  -webkit-text-fill-color: var(--foreground-color, white) !important;
}

/* Light Theme Styles */
[data-theme="light"] .auth-input :deep(.n-input__input-el) {
  --foreground-color: #333 !important;
}

[data-theme="light"] .auth-input :deep(.n-input__prefix) {
  color: #666 !important;
}

[data-theme="light"] .auth-input :deep(.n-input) {
  background-color: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(0, 0, 0, 0.2) !important;
}

[data-theme="light"] .auth-input :deep(.n-input:hover) {
  border-color: rgba(100, 108, 255, 0.7) !important;
  background-color: #ffffff !important;
}

[data-theme="light"] .auth-input :deep(.n-input:focus-within) {
  border-color: #646cff !important;
  background-color: #ffffff !important;
}

[data-theme="light"] .auth-input :deep(.n-input__placeholder) {
  color: #666 !important;
}

.auth-input {
  width: 100%;
}

/* Submit Button */
.auth-submit-btn {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%) !important;
  border: none !important;
  border-radius: 0.75rem !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  transition: all 0.3s ease !important;
}

.auth-submit-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 8px 25px rgba(100, 108, 255, 0.3) !important;
}

.button-icon {
  width: 18px;
  height: 18px;
}

/* Alert Styling */
.auth-alert {
  margin-top: 1rem;
}

.auth-alert :deep(.n-alert) {
  background-color: rgba(255, 82, 82, 0.1) !important;
  border: 1px solid rgba(255, 82, 82, 0.3) !important;
  border-radius: 0.75rem !important;
}

/* Footer */
.login-footer {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.login-footer p {
  margin: 0;
  color: #a0a0a0;
}

.auth-link {
  color: #646cff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #535bf2;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 0.75rem 0;
  }
  
  .logo-brand {
    display: none;
  }
  
  .header-actions {
    gap: 0.5rem;
  }
  
  .login-container {
    margin-top: 4rem;
  }
}

@media (max-width: 640px) {
  .login-card {
    padding: 2rem 1.5rem;
    margin: 1rem;
    border-radius: 1rem;
  }
  
  .brand-logo {
    font-size: 2rem;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
  
  .header-actions .signup-btn {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.875rem !important;
  }
}

/* RTL Support */
.rtl .header-content {
  direction: rtl;
}

.rtl .logo {
  flex-direction: row-reverse;
}

.rtl .header-actions {
  flex-direction: row-reverse;
}

/* Light Theme Styles */
[data-theme="light"] .login-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
}

[data-theme="light"] .header {
  background: rgba(248, 250, 252, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .logo-text,
[data-theme="light"] .logo-brand {
  color: #1e293b !important;
}

[data-theme="light"] .floating-shapes .shape {
  opacity: 0.4;
}

[data-theme="light"] .shape-1 {
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.4) 0%, rgba(219, 39, 119, 0.3) 100%);
  box-shadow: 0 8px 32px rgba(236, 72, 153, 0.2);
}

[data-theme="light"] .shape-2 {
  background: linear-gradient(135deg, rgba(219, 39, 119, 0.35) 0%, rgba(100, 108, 255, 0.25) 100%);
  box-shadow: 0 8px 32px rgba(219, 39, 119, 0.15);
}

[data-theme="light"] .shape-3 {
  background: linear-gradient(135deg, rgba(100, 108, 255, 0.3) 0%, rgba(236, 72, 153, 0.35) 100%);
  box-shadow: 0 8px 32px rgba(100, 108, 255, 0.2);
}

[data-theme="light"] .login-card {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(100, 108, 255, 0.15);
  color: #1e293b;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

[data-theme="light"] .brand-logo .gradient-text {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

[data-theme="light"] .login-title {
  color: #1e293b;
}

[data-theme="light"] .login-subtitle {
  color: rgba(30, 41, 59, 0.8);
}

[data-theme="light"] .form-item {
  color: #1e293b;
}

[data-theme="light"] .forgot-password {
  color: #646cff;
}

[data-theme="light"] .forgot-password:hover {
  color: #535bf2;
}

[data-theme="light"] .signup-link {
  color: rgba(30, 41, 59, 0.8);
}

[data-theme="light"] .signup-link a {
  color: #646cff;
}

[data-theme="light"] .signup-link a:hover {
  color: #535bf2;
}

[data-theme="light"] .language-selector {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(100, 108, 255, 0.2) !important;
  color: #1e293b !important;
}

[data-theme="light"] .language-selector:hover {
  background: rgba(255, 255, 255, 1) !important;
  border-color: rgba(100, 108, 255, 0.3) !important;
}

/* Verification Section Styles */
.resend-verification-section {
  margin-top: 1rem;
}

.verification-info {
  background: rgba(59, 130, 246, 0.1) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  border-radius: 8px !important;
}

.verification-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: center;
  text-align: center;
}

.verification-content p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
}

.resend-btn {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%) !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 0.5rem 1rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.resend-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(100, 108, 255, 0.3) !important;
}

.verification-sent-section {
  margin-top: 1rem;
}

.verification-sent {
  background: rgba(34, 197, 94, 0.1) !important;
  border: 1px solid rgba(34, 197, 94, 0.2) !important;
  border-radius: 8px !important;
}

/* Light theme overrides for verification sections */
[data-theme="light"] .verification-content p {
  color: rgba(30, 41, 59, 0.8);
}

[data-theme="light"] .verification-info {
  background: rgba(59, 130, 246, 0.05) !important;
  border: 1px solid rgba(59, 130, 246, 0.15) !important;
}

[data-theme="light"] .verification-sent {
  background: rgba(34, 197, 94, 0.05) !important;
  border: 1px solid rgba(34, 197, 94, 0.15) !important;
}
</style>
